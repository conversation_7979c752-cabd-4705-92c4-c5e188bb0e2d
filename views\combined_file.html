
<!DOCTYPE html>
<html lang="vi">
<head>
    <!-- ========================================
         META TAGS & BASIC SETUP
         ======================================== -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Generator </title>

    <!-- ========================================
         CACHE CONTROL
         ======================================== -->
    <!-- Prevent caching for dynamic content - Ngăn cache cho nội dung động -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <!-- ========================================
         FONTS & TYPOGRAPHY
         ======================================== -->
    <!-- Vietnamese-supported Fonts - Font hỗ trợ tiếng Việt -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800&family=Open+Sans:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- ========================================
         EXTERNAL STYLESHEETS
         ======================================== -->
    <!-- Apple Design System - CSS chính cho giao diện Apple -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/apple-design.css') }}?v={{ range(1000, 9999) | random }}">

    <!-- ========================================
         CUSTOM STYLES FOR SINGLE PAGE LAYOUT
         ======================================== -->
    <style>
        /* CAMERA OPTIMIZATION - MAXIMUM DISPLAY AREA */
        /* GLOBAL SCROLL & ANIMATION */
        html { scroll-behavior: smooth; }
        body.no-scroll { overflow: hidden; height: 100vh; }
        body.allow-scroll { overflow-y: auto; height: auto; }

        /* MAIN LAYOUT - ABSOLUTE MAXIMUM SPACE */
        .single-page-container {
            max-width: 1900px;                          /* MAXIMUM width */
            margin: 60px auto 0 !important;             /* REDUCED margin even more */
            padding: 2px !important;                     /* ABSOLUTE MINIMAL padding */
            min-height: calc(100vh - 60px) !important;   /* More space */
        }

        /* PAGE HEADER - MAXIMUM COMPACT for camera space */
        .page-header {
            text-align: center;
            margin-bottom: 4px !important;              /* ULTRA MINIMAL spacing */
            padding: 8px 0 !important;                  /* ULTRA REDUCED padding */
            background: var(--bg-hero);
            border-radius: 8px !important;              /* MINIMAL radius */
        }

        .page-title {
            font-size: clamp(20px, 2.5vw, 28px) !important; /* ULTRA COMPACT font */
            font-weight: var(--font-weight-bold);
            color: var(--color-text-white);
            margin-bottom: 2px !important;              /* ULTRA MINIMAL spacing */
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            font-size: 18px;
            color: rgba(255,255,255,0.8);
            margin: 0;
        }

        /* PROGRESSIVE DISCLOSURE & ANIMATIONS */

        /* Hidden section - Section ẩn ban đầu */
        .section-hidden {
            opacity: 0;                                  /* Trong suốt hoàn toàn */
            transform: translateY(30px);                 /* Dịch chuyển xuống */
            transition: all 0.6s ease-in-out;          /* Hiệu ứng chuyển đổi */
            pointer-events: none;                        /* Không thể tương tác */
            max-height: 0;                              /* Chiều cao 0 */
            overflow: hidden;                           /* Ẩn nội dung tràn */
        }

        /* Visible section - Section hiển thị */
        .section-visible {
            opacity: 1;                                  /* Hiển thị hoàn toàn */
            transform: translateY(0);                    /* Vị trí bình thường */
            transition: all 0.6s ease-in-out;          /* Hiệu ứng chuyển đổi */
            pointer-events: auto;                        /* Có thể tương tác */
            max-height: none;                           /* Chiều cao tự động */
            overflow: visible;                          /* Hiển thị nội dung */
        }

        /* Slide in animation - Hiệu ứng trượt vào */
        .slide-in {
            animation: slideInUp 0.6s ease-out forwards;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Fade in animation - Hiệu ứng mờ dần */
        .fade-in {
            animation: fadeIn 0.8s ease-out forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* WORKFLOW SECTIONS */

        /* Workflow sections - ULTRA COMPACT for maximum camera space */
        .workflow-section {
            margin-bottom: var(--spacing-xs);            /* MINIMAL spacing */
            background: var(--bg-card);                  /* Background card */
            border-radius: var(--radius-md);             /* SMALLER border radius */
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.25); /* Giảm đổ bóng */
            overflow: hidden;                            /* Ẩn nội dung tràn */
            border: 2px solid rgba(255,255,255,0.3);     /* Giảm viền */
            backdrop-filter: blur(20px);                 /* Giảm hiệu ứng blur */
            scroll-margin-top: 60px;                     /* REDUCED scroll margin */
        }

        /* Section header - Header của từng section */
        .section-header {
            background: var(--primary-gradient);         /* Background gradient */
            color: white;                                /* Màu chữ trắng */
            padding: var(--spacing-lg);                  /* Padding */
            text-align: center;                          /* Căn giữa */
            position: relative;                          /* Để định vị status */
        }

        /* Section title - Tiêu đề section */
        .section-title {
            font-size: 24px;                            /* Kích thước font */
            font-weight: var(--font-weight-bold);       /* Độ đậm */
            margin: 0;                                   /* Không margin */
            display: flex;                               /* Flexbox */
            align-items: center;                         /* Căn giữa theo chiều dọc */
            justify-content: center;                     /* Căn giữa theo chiều ngang */
            gap: var(--spacing-sm);                      /* Khoảng cách giữa icon và text */
        }

        /* Section subtitle - Phụ đề section */
        .section-subtitle {
            color: rgba(255, 255, 255, 0.8);            /* Màu chữ trắng trong suốt */
            margin: var(--spacing-xs) 0 0 0;            /* Margin trên */
            font-size: 16px;                            /* Kích thước font */
        }

        /* STATUS INDICATORS */

        /* Section status - Trạng thái section */
        .section-status {
            position: absolute;                          /* Định vị tuyệt đối */
            top: var(--spacing-md);                      /* Vị trí từ trên */
            right: var(--spacing-lg);                    /* Vị trí từ phải */
            display: flex;                               /* Flexbox */
            align-items: center;                         /* Căn giữa theo chiều dọc */
            gap: var(--spacing-xs);                      /* Khoảng cách giữa các element */
        }

        /* Status indicator dot - Chấm trạng thái */
        .status-indicator {
            width: 12px;                                 /* Chiều rộng */
            height: 12px;                                /* Chiều cao */
            border-radius: 50%;                          /* Hình tròn */
            background: var(--color-warning);            /* Màu mặc định */
        }

        /* Status colors - Màu sắc trạng thái */
        .status-pending { background: var(--color-warning); }     /* Chờ xử lý */
        .status-processing {                                      /* Đang xử lý */
            background: var(--color-primary);
            animation: pulse 2s infinite;                         /* Hiệu ứng nhấp nháy */
        }
        .status-complete { background: var(--color-success); }    /* Hoàn thành */
        .status-error { background: var(--color-error); }        /* Lỗi */

        /* Pulse animation - Hiệu ứng nhấp nháy */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Section body - Nội dung section (ultra compact) */
        .section-body {
            padding: var(--spacing-sm);                  /* Giảm padding tối đa để gọn nhất */
        }

        /* CAMERA SECTION STYLES */

        /* Camera section - CONTRASTING BACKGROUND for visual separation */
        #cameraSection {
            background: linear-gradient(135deg,
                rgba(30, 41, 59, 0.95) 0%,       /* Dark slate */
                rgba(51, 65, 85, 0.92) 25%,      /* Slate gray */
                rgba(30, 41, 59, 0.95) 50%,      /* Dark slate */
                rgba(51, 65, 85, 0.92) 75%,      /* Slate gray */
                rgba(30, 41, 59, 0.95) 100%      /* Dark slate */
            ) !important;
            border: 3px solid rgba(102, 126, 234, 0.6);  /* Bright purple border for contrast */
            position: relative;                           /* Để thêm overlay nếu cần */
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);  /* Dark shadow for depth */
        }

        /* Camera section overlay - Enhanced contrast texture */
        #cameraSection::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%,
                rgba(102, 126, 234, 0.15) 0%,
                rgba(67, 56, 202, 0.1) 30%,
                transparent 60%
            );
            pointer-events: none;                         /* Không ảnh hưởng đến tương tác */
            border-radius: inherit;                       /* Kế thừa border-radius */
        }

        /* Camera section text contrast - Đảm bảo text rõ ràng trên gradient */
        #cameraSection .camera-title,
        #cameraSection .control-title,
        #cameraSection .apple-select,
        #cameraSection .apple-button {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);   /* Text shadow nhẹ */
        }

        #cameraSection .section-body {
            position: relative;                           /* Đảm bảo content nằm trên overlay */
            z-index: 1;                                  /* Z-index cao hơn overlay */
        }

        /* Camera grid - ABSOLUTE MAXIMUM SPACE */
        .camera-grid {
            display: grid !important;                    /* CSS Grid */
            grid-template-columns: 1fr 1fr !important;  /* 2 cột bằng nhau hoàn toàn */
            gap: 4px !important;                         /* ABSOLUTE MINIMAL gap */
            margin-bottom: 0px !important;              /* NO margin bottom */
            align-items: stretch !important;             /* Căn đều chiều cao */
            min-height: 500px !important;               /* MUCH TALLER cameras */
            padding: 2px !important;                     /* ABSOLUTE MINIMAL padding */
        }

        /* Camera container - MAXIMUM SPACE EFFICIENCY */
        .camera-container {
            background: rgba(102, 126, 234, 0.08) !important; /* Background tím nhẹ */
            border-radius: 8px !important;               /* MINIMAL border radius */
            overflow: hidden !important;                 /* Ẩn nội dung tràn */
            border: 1px solid rgba(102, 126, 234, 0.25) !important; /* THINNER border */
            display: flex !important;                    /* Flexbox để kiểm soát layout */
            flex-direction: column !important;           /* Sắp xếp theo cột */
            height: 100% !important;                     /* Chiều cao 100% để cân đối */
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1) !important; /* LIGHTER shadow */
        }

        /* Camera header - Header của mỗi camera (BEAUTIFUL GRADIENT) */
        .camera-header {
            background: linear-gradient(135deg,
                #667eea 0%,
                #764ba2 50%,
                #667eea 100%
            );                                          /* Gradient tím đẹp */
            padding: 6px 8px !important;                /* ULTRA MINIMAL padding */
            display: flex;                               /* Flexbox */
            justify-content: space-between;              /* Căn đều 2 đầu */
            align-items: center;                         /* Căn giữa theo chiều dọc */
            min-height: 35px !important;                /* ULTRA COMPACT height */
            flex-shrink: 0;                             /* Không co lại */
            color: white;                               /* Màu chữ trắng */
            font-weight: var(--font-weight-semibold);   /* Độ đậm cao */
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3); /* Shadow header */
        }

        /* Camera info - Thông tin camera (icon + title) */
        .camera-info {
            display: flex;                               /* Flexbox */
            align-items: center;                         /* Căn giữa theo chiều dọc */
            gap: var(--spacing-sm);                      /* Khoảng cách giữa icon và title */
        }

        /* Camera icon - Icon camera (emoji - deprecated) */
        .camera-icon {
            font-size: 20px;                            /* Kích thước icon */
        }

        /* Camera icon image - MAXIMUM VISIBILITY and COMPACT */
        .camera-icon-img {
            height: 28px !important;                    /* COMPACT icon size */
            width: 28px !important;                     /* COMPACT icon size */
            object-fit: contain !important;             /* Giữ tỷ lệ */
            filter: brightness(0) invert(1) drop-shadow(0 1px 2px rgba(0,0,0,0.3)) !important; /* White with shadow */
            margin-right: 6px !important;              /* MINIMAL spacing from text */
            flex-shrink: 0 !important;                 /* Prevent shrinking */
        }

        /* Camera title - Tiêu đề camera (rõ ràng hơn) */
        .camera-title {
            color: white;                                /* Màu chữ trắng */
            margin: 0;                                   /* Không margin */
            font-size: 16px !important;                 /* COMPACT font size */
            font-weight: var(--font-weight-semibold);   /* Độ đậm font */
            white-space: nowrap;                        /* Không xuống dòng */
        }

        /* Camera status - Trạng thái camera */
        .camera-status {
            display: flex;                               /* Flexbox */
            align-items: center;                         /* Căn giữa theo chiều dọc */
            gap: var(--spacing-xs);                      /* Khoảng cách giữa dot và text */
            color: #ffffff !important;                   /* Màu chữ trắng (force override) */
            font-size: 14px;                            /* Kích thước font */
        }

        /* Camera status text - Text trạng thái camera */
        .camera-status span {
            color: #ffffff !important;                   /* Đảm bảo text màu trắng */
        }

        /* Status dot - Chấm trạng thái camera */
        .status-dot {
            width: 10px;                                 /* Chiều rộng */
            height: 10px;                                /* Chiều cao */
            border-radius: 50%;                          /* Hình tròn */
            background: var(--color-success);            /* Màu xanh */
        }

        /* CAMERA FRAME & STREAM */

        /* Camera frame - ABSOLUTE MAXIMUM SIZE */
        .camera-frame {
            position: relative !important;              /* Để định vị overlay */
            width: 100% !important;                     /* ABSOLUTE MAXIMUM WIDTH 100% */
            height: 0 !important;                       /* Chiều cao 0 để dùng padding-bottom */
            padding-bottom: 85% !important;            /* INCREASED HEIGHT - Much taller cameras */
            overflow: hidden !important;                /* Ẩn nội dung tràn */
            flex: 1 !important;                         /* Chiếm không gian còn lại */
            background: linear-gradient(135deg, #000 0%, #1a1a1a 100%) !important; /* Gradient đen */
            margin: 0 !important;                       /* NO margin for maximum space */
            border-radius: 3px !important;              /* ULTRA MINIMAL border radius */
            border: 1px solid #667eea !important;       /* THINNER border */
            box-sizing: border-box !important;          /* Đảm bảo viền không ảnh hưởng kích thước */
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.1) !important; /* MINIMAL shadow */
        }

        /* Camera stream - ABSOLUTE MAXIMUM SIZE VIDEO */
        .camera-stream {
            position: absolute !important;              /* Absolute positioning */
            top: 0 !important;                          /* Top 0 */
            left: 0 !important;                         /* Left 0 */
            width: 100% !important;                     /* ABSOLUTE MAXIMUM WIDTH */
            height: 100% !important;                    /* ABSOLUTE MAXIMUM HEIGHT */
            object-fit: cover !important;               /* Cắt để vừa khung */
            display: block !important;                  /* Block element */
            border-radius: 3px !important;              /* ULTRA MINIMAL border radius */
        }

        /* ========================================
           CAMERA OVERLAY GUIDES
           ======================================== */

        /* Camera overlay guides - Hướng dẫn overlay trên camera (cân đối) */
        .camera-overlay-guides {
            position: absolute;                          /* Định vị tuyệt đối */
            top: 0;                                      /* Từ trên cùng */
            left: 0;                                     /* Từ trái */
            right: 0;                                    /* Đến phải */
            bottom: 0;                                   /* Đến dưới */
            pointer-events: none;                        /* Không chặn click */
            z-index: 10;                                /* Đảm bảo hiển thị trên camera */
        }

        /* Guide corner - Góc hướng dẫn */
        .guide-corner {
            position: absolute;                          /* Định vị tuyệt đối */
            width: 20px;                                 /* Chiều rộng */
            height: 20px;                                /* Chiều cao */
            border: 2px solid rgba(255, 255, 255, 0.8); /* Viền trắng trong suốt */
        }

        /* Guide corner positions - Vị trí các góc */
        .guide-corner.tl {                              /* Top Left - Góc trên trái */
            top: 10px;
            left: 10px;
            border-right: none;                          /* Không viền phải */
            border-bottom: none;                         /* Không viền dưới */
        }

        .guide-corner.tr {                              /* Top Right - Góc trên phải */
            top: 10px;
            right: 10px;
            border-left: none;                           /* Không viền trái */
            border-bottom: none;                         /* Không viền dưới */
        }

        .guide-corner.bl {                              /* Bottom Left - Góc dưới trái */
            bottom: 10px;
            left: 10px;
            border-right: none;                          /* Không viền phải */
            border-top: none;                            /* Không viền trên */
        }

        .guide-corner.br {                              /* Bottom Right - Góc dưới phải */
            bottom: 10px;
            right: 10px;
            border-left: none;                           /* Không viền trái */
            border-top: none;                            /* Không viền trên */
        }

        /* ========================================
           CONTROLS SECTION STYLES
           ======================================== */

        /* Controls grid - Lưới điều khiển */
        .controls-grid {
            display: grid;                               /* CSS Grid */
            grid-template-columns: 1fr 1fr;             /* 2 cột bằng nhau */
            gap: var(--spacing-xl);                      /* Khoảng cách giữa các control */
            align-items: start;                          /* Căn đầu theo chiều dọc */
        }

        /* Control group - Nhóm điều khiển */
        .control-group {
            background: rgba(255, 255, 255, 0.1);       /* Background trong suốt */
            border-radius: var(--radius-lg);             /* Bo góc */
            padding: var(--spacing-lg);                  /* Padding bên trong */
            border: 1px solid rgba(255, 255, 255, 0.2); /* Viền trắng trong suốt */
        }

        /* Control title - Tiêu đề điều khiển */
        .control-title {
            color: var(--color-text);                    /* Màu chữ */
            font-size: 18px;                            /* Kích thước font */
            font-weight: var(--font-weight-semibold);   /* Độ đậm font */
            margin-bottom: var(--spacing-md);           /* Khoảng cách dưới */
            display: flex;                               /* Flexbox */
            align-items: center;                         /* Căn giữa theo chiều dọc */
            gap: var(--spacing-sm);                      /* Khoảng cách giữa icon và text */
        }

        /* ========================================
           RESULTS SECTION STYLES
           ======================================== */

        /* Results grid - Lưới hiển thị kết quả (tuần tự, không song song) */
        .results-grid {
            display: flex;                               /* Flexbox thay vì Grid */
            flex-direction: column;                      /* Hiển thị theo cột (tuần tự) */
            gap: var(--spacing-xl);                      /* Khoảng cách giữa các card */
        }

        /* Result card - Card hiển thị kết quả (full width, tuần tự) */
        .result-card {
            background: rgba(255, 255, 255, 0.1);       /* Background trong suốt */
            border-radius: var(--radius-lg);             /* Bo góc */
            border: 1px solid rgba(255, 255, 255, 0.2); /* Viền trắng trong suốt */
            overflow: hidden;                            /* Ẩn nội dung tràn */
            min-height: 180px;                          /* Giảm chiều cao tối thiểu */
            scroll-margin-top: 100px;                    /* Giảm margin cho scroll anchor */
            width: 100%;                                /* Full width */
        }

        /* Result header - Header của card kết quả */
        .result-header {
            background: var(--warning-gradient);         /* Background gradient vàng */
            color: white;                                /* Màu chữ trắng */
            padding: var(--spacing-md);                  /* Padding */
            text-align: center;                          /* Căn giữa text */
        }

        /* Result title - Tiêu đề card kết quả */
        .result-title {
            color: white;                                /* Màu chữ trắng */
            margin: 0;                                   /* Không margin */
            font-size: 16px;                            /* Kích thước font */
            font-weight: var(--font-weight-semibold);   /* Độ đậm font */
        }

        /* Result body - Nội dung card kết quả */
        .result-body {
            padding: var(--spacing-lg);                  /* Padding lớn */
        }

        /* ========================================
           CARD INFO DISPLAY STYLES
           ======================================== */

        /* Card info grid - Lưới hiển thị thông tin card */
        .card-info-grid {
            display: grid;                                        /* CSS Grid */
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Cột tự động, tối thiểu 200px */
            gap: var(--spacing-md);                               /* Khoảng cách giữa các item */
        }

        /* Card info item - Item thông tin card */
        .card-info-item {
            background: rgba(255, 255, 255, 0.05);               /* Background rất nhạt */
            border-radius: var(--radius-md);                     /* Bo góc vừa */
            padding: var(--spacing-md);                          /* Padding vừa */
            text-align: center;                                  /* Căn giữa text */
        }

        /* Card info label - Label thông tin card */
        .card-info-label {
            display: block;                                      /* Hiển thị block */
            font-size: 14px;                                    /* Font nhỏ */
            color: var(--color-text-secondary);                 /* Màu chữ phụ */
            margin-bottom: var(--spacing-xs);                   /* Khoảng cách dưới nhỏ */
            font-weight: var(--font-weight-medium);             /* Độ đậm vừa */
        }

        /* Card info value - Giá trị thông tin card */
        .card-info-value {
            display: block;                                      /* Hiển thị block */
            font-size: 16px;                                    /* Font lớn hơn label */
            color: var(--color-text);                           /* Màu chữ chính */
            font-weight: var(--font-weight-semibold);           /* Độ đậm cao */
        }

        /* ========================================
           IMAGES DISPLAY STYLES
           ======================================== */

        /* Images grid - Lưới hiển thị ảnh */
        .images-grid {
            display: grid;                                        /* CSS Grid */
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Cột tự động, tối thiểu 250px */
            gap: var(--spacing-lg);                               /* Khoảng cách lớn giữa các ảnh */
        }

        /* Image item - Item chứa ảnh */
        .image-item {
            background: rgba(255, 255, 255, 0.05);               /* Background rất nhạt */
            border-radius: var(--radius-lg);                     /* Bo góc lớn */
            overflow: hidden;                                    /* Ẩn nội dung tràn */
        }

        /* Image display - Container hiển thị ảnh */
        .image-display {
            position: relative;                                  /* Để định vị overlay */
            aspect-ratio: 4/3;                                  /* Tỷ lệ 4:3 */
            overflow: hidden;                                    /* Ẩn nội dung tràn */
        }

        /* Image display img - Ảnh trong container */
        .image-display img {
            width: 100%;                                         /* Chiều rộng 100% */
            height: 100%;                                        /* Chiều cao 100% */
            object-fit: cover;                                   /* Cắt để vừa khung */
        }

        /* Image overlay - Overlay trên ảnh */
        .image-overlay {
            position: absolute;                                  /* Định vị tuyệt đối */
            bottom: 0;                                          /* Ở dưới cùng */
            left: 0;                                            /* Từ trái */
            right: 0;                                           /* Đến phải */
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)); /* Gradient từ trong suốt đến đen */
            color: white;                                        /* Màu chữ trắng */
            padding: var(--spacing-md);                          /* Padding vừa */
            font-weight: var(--font-weight-semibold);           /* Độ đậm cao */
        }

        /* Image actions - Khu vực hành động (nút download) */
        .image-actions {
            padding: var(--spacing-md);                          /* Padding vừa */
            text-align: center;                                  /* Căn giữa */
        }

        /* Download button - Nút tải về */
        .download-btn {
            display: inline-block;                               /* Hiển thị inline-block */
            background: var(--success-gradient);                 /* Background gradient xanh */
            color: white;                                        /* Màu chữ trắng */
            padding: var(--spacing-sm) var(--spacing-md);       /* Padding nhỏ dọc, vừa ngang */
            border-radius: var(--radius-md);                     /* Bo góc vừa */
            text-decoration: none;                               /* Không gạch chân */
            font-weight: var(--font-weight-semibold);           /* Độ đậm cao */
            transition: all 0.3s ease;                          /* Hiệu ứng chuyển đổi */
        }

        /* Download button hover - Hiệu ứng hover nút tải về */
        .download-btn:hover {
            transform: translateY(-2px);                         /* Di chuyển lên 2px */
            box-shadow: 0 8px 25px rgba(67, 233, 123, 0.4);     /* Đổ bóng xanh */
        }

        /* ========================================
           EMPTY STATE STYLES
           ======================================== */

        /* Empty state - Trạng thái rỗng khi không có dữ liệu */
        .empty-state {
            text-align: center;                                  /* Căn giữa text */
            padding: var(--spacing-3xl);                        /* Padding rất lớn */
            color: var(--color-text-secondary);                 /* Màu chữ phụ */
        }

        /* Empty state icon - Icon trạng thái rỗng */
        .empty-state-icon {
            font-size: 48px;                                    /* Font rất lớn */
            margin-bottom: var(--spacing-md);                   /* Khoảng cách dưới vừa */
            opacity: 0.5;                                       /* Độ trong suốt 50% */
        }

        /* ========================================
           NAVIGATION BAR CUSTOM STYLES
           ======================================== */

        /* Custom navigation styles - Override default apple-nav */
        .apple-nav {
            height: 80px !important;                            /* Tăng chiều cao navigation */
            display: flex !important;
            align-items: center !important;
        }

        .apple-nav-content {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;                /* Căn giữa */
            width: 100% !important;
            max-width: 1200px !important;
            margin: 0 auto !important;
            padding: 0 var(--spacing-lg) !important;
        }

        .apple-logo {
            display: flex !important;
            align-items: center !important;
            gap: var(--spacing-md) !important;                  /* Khoảng cách giữa logo và text */
            text-decoration: none !important;
            width: auto !important;                             /* Override width 100% */
        }

        .nav-logo-img {
            height: 60px !important;                           /* Tăng kích thước logo */
            width: auto !important;
            object-fit: contain !important;
        }

        .nav-title {
            color: white !important;
            font-size: 28px !important;
            font-weight: 800 !important;
            margin: 0 !important;
        }

        /* ========================================
           COMBINED CONTROLS STYLES
           ======================================== */

        /* Combined controls - MAXIMUM COMPACT for camera space */
        .combined-controls {
            background: linear-gradient(135deg,
                rgba(102, 126, 234, 0.1) 0%,
                rgba(118, 75, 162, 0.08) 100%
            );                                          /* Gradient background */
            border-radius: 4px !important;              /* ULTRA MINIMAL border radius */
            padding: 6px !important;                     /* ULTRA MINIMAL padding */
            border: 1px solid rgba(102, 126, 234, 0.2) !important; /* THINNER border */
            margin-top: 0px !important;                 /* NO margin top */
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.08) !important; /* LIGHTER shadow */
        }

        /* Controls grid improvements - MAXIMUM COMPACT for camera space */
        .combined-controls .controls-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;             /* 2 cột bằng nhau hoàn toàn */
            gap: 4px !important;                         /* ULTRA MINIMAL gap */
            align-items: stretch;                        /* Căn đều chiều cao */
            margin-bottom: 0;                           /* Không margin bottom */
            align-content: center;                       /* Căn giữa nội dung */
            min-height: 50px !important;                /* MAXIMUM COMPACT height */
        }

        /* Control group improvements - MAXIMUM COMPACT for camera space */
        .combined-controls .control-group {
            margin: 0;
            padding: 4px !important;                     /* ULTRA MINIMAL padding */
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 255, 255, 0.05) 100%
            );                                          /* Gradient background */
            border-radius: 3px !important;              /* ULTRA MINIMAL border radius */
            border: 1px solid rgba(102, 126, 234, 0.2); /* Viền tím nhẹ */
            display: flex;
            flex-direction: column;
            justify-content: center;                     /* Căn giữa nội dung */
            align-items: stretch;                        /* Căn đều chiều rộng */
            min-height: 50px !important;                /* MAXIMUM COMPACT height */
            height: 100%;                               /* Chiều cao 100% để đồng đều */
            box-shadow: 0 1px 8px rgba(102, 126, 234, 0.08) !important; /* LIGHTER shadow */
        }

        .combined-controls .control-title {
            margin-bottom: var(--spacing-xs);           /* REDUCED margin */
            font-size: 14px;                            /* SMALLER font size */
            font-weight: var(--font-weight-semibold);
            text-align: center;                          /* Căn giữa title */
            display: flex;                               /* Flexbox để căn icon */
            align-items: center;                         /* Căn giữa icon và text */
            justify-content: center;                     /* Căn giữa nội dung */
            gap: var(--spacing-xs);                      /* Khoảng cách giữa icon và text */
        }

        .combined-controls .control-title span {
            font-size: 16px;                            /* SMALLER icon size */
            line-height: 1;                             /* Line height chuẩn */
        }

        /* ========================================
           CAMERA LAYOUT DEBUG & IMPROVEMENTS
           ======================================== */

        /* Debug: MAXIMUM CAMERA SIZE - ULTRA TALL CAMERAS */
        .camera-container:nth-child(1),
        .camera-container:nth-child(2) {
            min-height: 450px !important;              /* MUCH TALLER cameras */
            max-height: 450px !important;              /* MUCH TALLER max height */
        }

        /* Đảm bảo camera info và status có layout nhất quán */
        .camera-info {
            flex: 1;                                    /* Chiếm không gian còn lại */
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .camera-status {
            flex-shrink: 0;                             /* Không co lại */
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 15px;                           /* INCREASED font size by 2px */
        }

        /* Enhanced camera status text - MAXIMUM READABILITY */
        #cardStatus, #faceStatus {
            font-size: 20px !important;                /* INCREASED to 20px for maximum readability */
            font-weight: 600 !important;               /* Semi-bold for better readability */
            color: white !important;                   /* Ensure white text on dark header */
            text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important; /* Text shadow for contrast */
            line-height: 1.2 !important;               /* Optimal line height */
        }

        /* ========================================
           BUTTON & SELECT STYLING IMPROVEMENTS
           ======================================== */

        /* Apple select styling - Cải thiện select box (BEAUTIFUL DESIGN) */
        .combined-controls .apple-select {
            width: 100% !important;
            font-size: 15px !important;                 /* Font size rõ ràng */
            padding: 14px 18px !important;              /* Padding thoải mái */
            border: 2px solid rgba(102, 126, 234, 0.4) !important; /* Viền tím */
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(248, 250, 252, 0.95) 100%
            ) !important;                               /* Gradient background */
            border-radius: var(--radius-md) !important;
            min-height: 48px !important;                /* Chiều cao thoải mái */
            box-sizing: border-box !important;          /* Đảm bảo tính toán kích thước chính xác */
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.1) !important; /* Shadow nhẹ */
        }

        /* Apple button styling - Cải thiện button (STUNNING DESIGN) */
        .combined-controls .apple-button {
            width: 100% !important;
            font-size: 15px !important;                 /* Font size rõ ràng */
            padding: 14px 18px !important;              /* Padding thoải mái */
            border-radius: var(--radius-md) !important;
            min-height: 48px !important;                /* Chiều cao thoải mái */
            box-sizing: border-box !important;          /* Đảm bảo tính toán kích thước chính xác */
            display: flex !important;                   /* Flexbox để căn giữa nội dung */
            align-items: center !important;             /* Căn giữa theo chiều dọc */
            justify-content: center !important;         /* Căn giữa theo chiều ngang */
            background: linear-gradient(135deg,
                #667eea 0%,
                #764ba2 100%
            ) !important;                               /* Gradient tím đẹp */
            border: none !important;                    /* Không viền */
            color: white !important;                    /* Chữ trắng */
            font-weight: 600 !important;               /* Font weight đậm */
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important; /* Shadow đẹp */
            transition: all 0.3s ease !important;      /* Transition mượt */
        }

        /* Button hover effects - Hiệu ứng hover cho button */
        .combined-controls .apple-button:hover {
            transform: translateY(-2px) !important;     /* Nâng lên nhẹ */
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important; /* Shadow đậm hơn */
            background: linear-gradient(135deg,
                #7c8cff 0%,
                #8a5fb8 100%
            ) !important;                               /* Gradient sáng hơn khi hover */
        }

        /* Select hover effects - Hiệu ứng hover cho select */
        .combined-controls .apple-select:hover {
            border-color: rgba(102, 126, 234, 0.6) !important; /* Viền đậm hơn */
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2) !important; /* Shadow đậm hơn */
        }

        /* Button group container - ADJACENT BUTTON LAYOUT */
        .button-group-container {
            display: flex !important;                  /* Flexbox layout */
            gap: var(--spacing-sm) !important;         /* Gap between buttons */
            align-items: center !important;            /* Center alignment */
            justify-content: center !important;        /* Center the group */
            flex-wrap: wrap !important;                /* Wrap on small screens */
        }

        /* Main action button - FULL SIZE */
        .main-action-button {
            flex: 1 !important;                        /* Take available space */
            min-width: 200px !important;               /* Minimum width */
            max-width: 300px !important;               /* Maximum width */
        }

        /* Adjacent retake button - COMPACT SIZE */
        .retake-button-adjacent {
            font-size: 14px !important;                /* Smaller font size */
            padding: 10px 16px !important;             /* Compact padding */
            min-height: 44px !important;               /* Match main button height */
            min-width: 120px !important;               /* Minimum width */
            max-width: 150px !important;               /* Maximum width constraint */
            flex-shrink: 0 !important;                 /* Don't shrink */
        }

        /* ========================================
           RESPONSIVE DESIGN
           ======================================== */

        /* Specific 1920x1080 optimization - Tối ưu cụ thể cho 1920x1080 */
        @media (min-width: 1920px) and (max-height: 1080px) {
            /* Tối ưu đặc biệt cho màn hình 1920x1080 - Camera cân đối hoàn hảo */
            .single-page-container {
                margin-top: 50px;                       /* Đảm bảo không bị header che */
                padding: var(--spacing-xs);              /* Padding tối thiểu */
            }

            .workflow-section {
                margin-bottom: var(--spacing-xs);        /* Khoảng cách tối thiểu */
            }

            /* Camera section responsive - Điều chỉnh gradient cho màn hình 1920x1080 */
            #cameraSection {
                border-width: 1px;                        /* Giảm độ dày viền */
            }

            .camera-grid {
                min-height: 300px;                       /* Chiều cao tối ưu */
                gap: var(--spacing-sm);                  /* Gap cân đối */
                padding: var(--spacing-xs);              /* Padding nhỏ hơn */
            }

            .camera-frame {
                width: 99% !important;                  /* MAXIMUM SIZE for 1920x1080 */
                padding-bottom: 74.25% !important;      /* Optimized aspect ratio */
                border-radius: 6px !important;          /* Minimal border radius */
                border-width: 1px !important;           /* Minimal border */
            }

            .combined-controls {
                margin-top: 0;                           /* Không margin top */
                padding: var(--spacing-xs);              /* Padding tối thiểu */
            }
        }

        /* Large desktop screens - Màn hình desktop lớn (1920px+) */
        @media (min-width: 1920px) {
            /* Tối ưu cho màn hình 1920x1080 - Đảm bảo không cần scroll */
            .single-page-container {
                max-width: 1850px;                       /* Tận dụng tối đa chiều rộng */
                padding: var(--spacing-xs);              /* Padding rất nhỏ */
                margin-top: 50px;                       /* Đảm bảo không bị header che */
            }

            .workflow-section {
                margin-bottom: var(--spacing-sm);        /* Giảm khoảng cách giữa sections */
            }

            /* Camera section responsive - Điều chỉnh gradient cho màn hình lớn */
            #cameraSection {
                border-width: 3px;                        /* Tăng độ dày viền cho màn hình lớn */
            }

            .section-body {
                padding: var(--spacing-sm);              /* Giảm padding section body */
            }

            .camera-grid {
                gap: var(--spacing-md);                  /* Gap cân đối */
                margin-bottom: var(--spacing-sm);        /* Margin bottom nhẹ */
                min-height: 350px;                       /* Chiều cao tối ưu cho màn hình lớn */
                padding: var(--spacing-sm);              /* Padding cân đối */
            }

            .camera-frame {
                width: 99.5% !important;                /* ABSOLUTE MAXIMUM SIZE */
                padding-bottom: 74.6% !important;       /* Optimized aspect ratio */
                border-radius: 8px !important;          /* Minimal border radius */
                border-width: 1px !important;           /* Minimal border */
            }

            .combined-controls {
                padding: var(--spacing-sm);              /* Giảm padding controls */
                margin-top: var(--spacing-xs);           /* Margin top tối thiểu */
            }

            .combined-controls .control-group {
                min-height: 100px;                       /* Giảm chiều cao tối thiểu */
                padding: var(--spacing-xs);              /* Giảm padding */
                justify-content: center;                 /* Đảm bảo căn giữa */
            }

            .combined-controls .controls-grid {
                gap: var(--spacing-sm);                  /* Giảm gap cho màn hình lớn */
            }
        }

        /* Tablet and small desktop - Màn hình tablet và desktop nhỏ */
        @media (max-width: 1200px) {
            /* Main content grid - Chuyển từ 2 cột thành 1 cột */
            .main-content-grid {
                grid-template-columns: 1fr;                     /* 1 cột duy nhất */
                gap: var(--spacing-xl);                          /* Khoảng cách lớn */
            }

            /* Results grid - Chuyển từ 2 cột thành 1 cột */
            .results-grid {
                grid-template-columns: 1fr;                     /* 1 cột duy nhất */
            }
        }

        /* Tablet devices - Thiết bị tablet */
        @media (max-width: 1024px) {
            /* Navigation adjustments - Điều chỉnh navigation */
            .apple-nav-content {
                padding: 0 var(--spacing-md) !important;        /* Padding nhỏ hơn */
            }

            .nav-title {
                font-size: 24px !important;                     /* Font nhỏ hơn */
            }
        }

        /* Mobile devices - Thiết bị di động */
        @media (max-width: 768px) {
            /* Navigation mobile - Navigation trên mobile */
            .apple-nav {
                height: 70px !important;                        /* Chiều cao nhỏ hơn */
            }

            .nav-logo-img {
                height: 50px !important;                        /* Logo nhỏ hơn */
            }

            .nav-title {
                font-size: 20px !important;                     /* Font nhỏ hơn */
            }

            /* Single page container - Giảm padding cho mobile */
            .single-page-container {
                padding: var(--spacing-md);                     /* Padding nhỏ hơn */
                margin-top: 50px;                               /* Margin top nhỏ hơn */
            }

            /* Camera grid - Chuyển thành 1 cột trên mobile */
            .camera-grid {
                grid-template-columns: 1fr;                     /* 1 cột duy nhất */
                gap: var(--spacing-lg);                         /* Khoảng cách vừa */
            }

            /* Combined controls - Điều chỉnh controls cho mobile */
            .combined-controls {
                padding: var(--spacing-lg);                     /* Padding nhỏ hơn */
                margin-top: var(--spacing-xl);                  /* Margin top nhỏ hơn */
            }

            /* Combined controls grid - Chuyển thành 1 cột trên mobile */
            .combined-controls .controls-grid {
                grid-template-columns: 1fr !important;          /* 1 cột duy nhất */
                gap: var(--spacing-lg) !important;              /* Khoảng cách vừa */
            }
        }

        /* Small mobile devices - Thiết bị mobile nhỏ */
        @media (max-width: 480px) {
            /* Navigation very small - Navigation rất nhỏ */
            .apple-nav-content {
                padding: 0 var(--spacing-sm) !important;        /* Padding rất nhỏ */
            }

            .nav-title {
                font-size: 18px !important;                     /* Font rất nhỏ */
            }

            /* Container very small - Container rất nhỏ */
            .single-page-container {
                padding: var(--spacing-sm);                     /* Padding rất nhỏ */
            }

            /* Combined controls very small - Controls rất nhỏ */
            .combined-controls {
                padding: var(--spacing-md);                     /* Padding nhỏ */
            }

            /* Animations on mobile - Animations trên mobile */
            .section-hidden {
                transform: translateY(20px);                    /* Dịch chuyển nhỏ hơn */
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);                /* Dịch chuyển nhỏ hơn */
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        }
    </style>

</head>
<body>
    <!-- ========================================
         NAVIGATION BAR
         ======================================== -->
    <!-- Clean Navigation - Thanh điều hướng chính -->
    <nav class="apple-nav">
        <div class="apple-nav-content">
            <!-- Logo and Title - Logo và tiêu đề (căn giữa) -->
            <a href="/" class="apple-logo">
                <!-- Logo Image - Hình ảnh logo -->
                <img src="{{ url_for('static', filename='images/ssg-logo.png') }}"
                     alt="SSG Logo"
                     class="nav-logo-img">
                <!-- Title Text - Text tiêu đề -->
                <span class="nav-title">AI Generator</span>
            </a>
        </div>
    </nav>

    <!-- ========================================
         MAIN CONTENT CONTAINER
         ======================================== -->
    <!-- Single Page Container - Container chính chứa toàn bộ nội dung -->
    <div class="single-page-container">



        <!-- ========================================
             COMBINED CAMERA & AI GENERATION SECTION
             ======================================== -->
        <!-- Combined Section - Gộp chụp ảnh và tạo AI -->
        <div id="cameraSection" class="workflow-section">
            <!-- Section Body - Nội dung chính của section gộp -->
            <div class="section-body">
                <!-- Camera Grid - Lưới hiển thị 2 camera -->
                <div class="camera-grid">

                    <!-- ========================================
                         BUSINESS CARD CAMERA
                         ======================================== -->
                    <!-- Business Card Camera - Camera chụp business card -->
                    <div class="camera-container">
                        <!-- Camera Header - Header của camera -->
                        <div class="camera-header">
                            <!-- Camera Info - Thông tin camera -->
                            <div class="camera-info">
                                <!-- Business Card Icon - Icon business card (cải thiện) -->
                                <img src="{{ url_for('static', filename='images/business-card-icon.png') }}"
                                     alt="Business Card"
                                     class="camera-icon-img">
                                <h3 class="camera-title">Business Card</h3> <!-- Tiêu đề có thể thay đổi -->
                            </div>
                            <!-- Camera Status - Trạng thái camera -->
                            <div class="camera-status">
                                <div id="status0" class="status-dot"></div>
                                <span id="cardStatus">Camera sẵn sàng - Đặt business card</span>
                            </div>
                        </div>
                        <!-- Camera Frame - Khung chứa video -->
                        <div class="camera-frame">
                            <!-- WebRTC Video Stream - Video stream WebRTC (ẩn mặc định) -->
                            <video id="cam0-webrtc" autoplay muted playsinline class="camera-stream" style="display: none;"></video>
                            <!-- Camera Image Stream - Image stream từ server -->
                            <img id="cam0" src="/video_feed/0" alt="Business Card Camera" class="camera-stream">
                            <!-- Overlay Guides - Hướng dẫn overlay -->
                            <div class="camera-overlay-guides">
                                <div class="guide-corner tl"></div> <!-- Top Left -->
                                <div class="guide-corner tr"></div> <!-- Top Right -->
                                <div class="guide-corner bl"></div> <!-- Bottom Left -->
                                <div class="guide-corner br"></div> <!-- Bottom Right -->
                            </div>
                        </div>
                    </div>

                    <!-- ========================================
                         FACE CAMERA
                         ======================================== -->
                    <!-- Face Camera - Camera chụp khuôn mặt -->
                    <div class="camera-container">
                        <!-- Camera Header - Header của camera -->
                        <div class="camera-header">
                            <!-- Camera Info - Thông tin camera -->
                            <div class="camera-info">
                                <!-- Face ID Icon - Icon face ID (cải thiện) -->
                                <img src="{{ url_for('static', filename='images/face-id-icon.png') }}"
                                     alt="Face ID"
                                     class="camera-icon-img">
                                <h3 class="camera-title">Face Photo</h3> <!-- Tiêu đề có thể thay đổi -->
                            </div>
                            <!-- Camera Status - Trạng thái camera -->
                            <div class="camera-status">
                                <div id="status1" class="status-dot"></div>
                                <span id="faceStatus">😊 Camera sẵn sàng - Đặt khuôn mặt</span>
                            </div>
                        </div>
                        <!-- Camera Frame - Khung chứa video -->
                        <div class="camera-frame">
                            <!-- WebRTC Video Stream - Video stream WebRTC (ẩn mặc định) -->
                            <video id="cam1-webrtc" autoplay muted playsinline class="camera-stream" style="display: none;"></video>
                            <!-- Camera Image Stream - Image stream từ server -->
                            <img id="cam1" src="/video_feed/1" alt="Face Camera" class="camera-stream">
                            <!-- Overlay Guides - Hướng dẫn overlay -->
                            <div class="camera-overlay-guides">
                                <div class="guide-corner tl"></div> <!-- Top Left -->
                                <div class="guide-corner tr"></div> <!-- Top Right -->
                                <div class="guide-corner bl"></div> <!-- Bottom Left -->
                                <div class="guide-corner br"></div> <!-- Bottom Right -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ========================================
                     COMBINED CONTROLS SECTION
                     ======================================== -->
                <!-- Combined Controls - Điều khiển gộp chụp ảnh và tạo AI -->
                <div class="combined-controls">
                    <!-- Controls Grid - Lưới điều khiển gộp -->
                    <div class="controls-grid">

                        <!-- Style Selection - Chọn style ảnh -->
                        <div class="control-group">
                            <h3 class="control-title">
                                <span>🎨</span> <!-- Icon có thể thay đổi -->
                                <p style="color: white;">Style ảnh</p> <!-- Text có thể thay đổi -->
                            </h3>
                            <!-- Style Select Dropdown - Dropdown chọn style -->
                            <select id="promptSelect" class="apple-select">
                                <option value="">Đang tải...</option> <!-- Option mặc định -->
                            </select>
                        </div>

                        <!-- Combined Action Button - Nút hành động gộp -->
                        <div class="control-group">
                            <h3 class="control-title">
                                <span>⚡</span> <!-- Icon có thể thay đổi -->
                                <p style="color: white;">Hành động</p> <!-- Text có thể thay đổi -->
                            </h3>
                            <!-- Button Group - Main and Retake buttons adjacent -->
                            <div class="button-group-container">
                                <!-- Combined Button - Nút gộp chụp ảnh và tạo AI -->
                                <button class="apple-button apple-button-primary main-action-button" id="combinedActionBtn" onclick="combinedCaptureAndGenerate()">
                                    <span style="font-size: 16px;">⚡</span> <!-- Icon có thể thay đổi -->
                                    Chụp ảnh & Tạo AI <!-- Text có thể thay đổi -->
                                </button>
                                <!-- Retake Button removed to save space -->
                            </div>
                        </div>
                    </div>

                    <!-- Style Description - Mô tả style đã chọn (ẩn) -->
                    <div id="promptDescription" style="display: none;">
                        Chọn style để xem mô tả <!-- Text mặc định -->
                    </div>

                    <!-- Progress Bar - Thanh tiến trình (ẩn mặc định) -->
                    <div id="progressBar" style="display: none;">
                        <!-- Progress Bar Container - Container thanh tiến trình -->
                        <div class="apple-progress-bar" style="height: 8px; background: rgba(255,255,255,0.1); border-radius: var(--radius-sm); margin-bottom: var(--spacing-sm);">
                            <!-- Progress Fill - Phần fill của thanh tiến trình -->
                            <div class="apple-progress-fill" id="progressFill" style="background: var(--primary-gradient); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                        </div>
                        <!-- Progress Text - Text mô tả tiến trình -->
                        <p style="font-size: 14px; color: var(--color-text-secondary); margin: 0; text-align: center;">
                            Đang xử lý... <!-- Text có thể thay đổi -->
                        </p>
                    </div>

                    <!-- Retake button moved to be adjacent to main button above -->
                </div>
            </div>
        </div>



        <!-- ========================================
             STEP 3: RESULTS DISPLAY
             ======================================== -->
        <!-- Step 3: Results Display - Bước 3: Hiển thị kết quả -->
        <div id="resultsSection" class="workflow-section section-hidden">
            <!-- Section Header - Header của section kết quả -->
            <div class="section-header">
                <!-- Status Indicator - Chỉ báo trạng thái -->
                <div class="section-status">
                    <div id="resultsStatus" class="status-indicator status-pending"></div>
                    <span id="resultsStatusText">Chờ xử lý</span>
                </div>
                <!-- Section Title - Tiêu đề section -->
                <h2 class="section-title">
                    <span>🎯</span> <!-- Icon có thể thay đổi -->
                    Bước 3: Kết quả
                </h2>
                <!-- Section Description - Mô tả chức năng -->
                <p class="section-subtitle">Thông tin đã trích xuất và ảnh AI đã tạo</p>
            </div>

            <!-- Section Body - Nội dung chính của section -->
            <div class="section-body">
                <!-- Results Grid - Lưới hiển thị kết quả -->
                <div class="results-grid">

                    <!-- ========================================
                         CARD INFORMATION DISPLAY
                         ======================================== -->
                    <!-- Card Information - Hiển thị thông tin business card -->
                    <div id="cardInfoSection" class="result-card section-hidden">
                        <!-- Result Header - Header của card kết quả -->
                        <div class="result-header">
                            <h3 class="result-title">📄 Thông tin Business Card</h3> <!-- Tiêu đề có thể thay đổi -->
                        </div>
                        <!-- Result Body - Nội dung card kết quả -->
                        <div class="result-body">
                            <!-- Card Info Grid - Lưới hiển thị thông tin card -->
                            <div id="cardInfoDisplay" class="card-info-grid">
                                <!-- Name Field - Trường tên -->
                                <div class="card-info-item">
                                    <span class="card-info-label">👤 Tên</span> <!-- Label có thể thay đổi -->
                                    <span class="card-info-value" id="cardName">Chưa có dữ liệu</span> <!-- Value sẽ được cập nhật bởi JS -->
                                </div>
                                <!-- Title Field - Trường chức vụ -->
                                <div class="card-info-item">
                                    <span class="card-info-label">💼 Chức vụ</span> <!-- Label có thể thay đổi -->
                                    <span class="card-info-value" id="cardTitle">Chưa có dữ liệu</span> <!-- Value sẽ được cập nhật bởi JS -->
                                </div>
                                <!-- Company Field - Trường công ty -->
                                <div class="card-info-item">
                                    <span class="card-info-label">🏢 Công ty</span> <!-- Label có thể thay đổi -->
                                    <span class="card-info-value" id="cardCompany">Chưa có dữ liệu</span> <!-- Value sẽ được cập nhật bởi JS -->
                                </div>
                                <!-- Email Field - Trường email -->
                                <div class="card-info-item">
                                    <span class="card-info-label">📧 Email</span> <!-- Label có thể thay đổi -->
                                    <span class="card-info-value" id="cardEmail">Chưa có dữ liệu</span> <!-- Value sẽ được cập nhật bởi JS -->
                                </div>
                                <!-- Phone Field - Trường điện thoại -->
                                <div class="card-info-item">
                                    <span class="card-info-label">📱 Điện thoại</span> <!-- Label có thể thay đổi -->
                                    <span class="card-info-value" id="cardPhone">Chưa có dữ liệu</span> <!-- Value sẽ được cập nhật bởi JS -->
                                </div>
                                <!-- Address Field - Trường địa chỉ -->
                                <div class="card-info-item">
                                    <span class="card-info-label">📍 Địa chỉ</span> <!-- Label có thể thay đổi -->
                                    <span class="card-info-value" id="cardAddress">Chưa có dữ liệu</span> <!-- Value sẽ được cập nhật bởi JS -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ========================================
                         GENERATED IMAGES DISPLAY
                         ======================================== -->
                    <!-- Generated Images - Hiển thị ảnh AI đã tạo -->
                    <div id="imagesSection" class="result-card section-hidden">
                        <!-- Result Header - Header của card ảnh -->
                        <div class="result-header">
                            <h3 class="result-title">🎨 Ảnh AI đã tạo</h3> <!-- Tiêu đề có thể thay đổi -->
                        </div>
                        <!-- Result Body - Nội dung card ảnh -->
                        <div class="result-body">
                            <!-- Images Grid - Lưới hiển thị ảnh (sẽ được cập nhật bởi JS) -->
                            <div id="generatedImagesDisplay" class="images-grid">
                                <!-- Empty State - Trạng thái rỗng khi chưa có ảnh -->
                                <div class="empty-state">
                                    <div class="empty-state-icon">🖼️</div> <!-- Icon có thể thay đổi -->
                                    <p>Chưa có ảnh AI nào được tạo</p> <!-- Text có thể thay đổi -->
                                    <p style="font-size: 14px; margin-top: var(--spacing-sm);">Chụp ảnh và tạo AI để xem kết quả</p> <!-- Hướng dẫn -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ========================================
                     ACTION BUTTONS
                     ======================================== -->
                <!-- Action Buttons - Các nút hành động -->
                <div style="text-align: center; margin-top: var(--spacing-xl);">
                    <!-- Create New Project Button - Nút bắt đầu dự án mới -->
                    <button id="createNewImagesBtn" class="apple-button apple-button-primary" onclick="console.log('BUTTON CLICKED!'); createNewImages();" style="font-size: 16px; margin-right: 15px; display: none;">
                        <span style="font-size: 18px;">🆕</span>
                        Dự án mới
                    </button>

                    <!-- Reset Session Button - Nút xóa session -->
                    <button class="apple-button apple-button-secondary" onclick="resetSession()" style="font-size: 16px;">
                        <span style="font-size: 18px;">🗑️</span>
                        Xóa session
                    </button>
                </div>
            </div>
        </div>

        <!-- ========================================
             STATUS MESSAGES
             ======================================== -->
        <!-- Status Messages - Thông báo trạng thái -->
        <div style="text-align: center; margin-bottom: var(--spacing-xl);">
            <div id="statusMessage" style="font-size: 16px; color: var(--color-text-secondary);"></div> <!-- Sẽ được cập nhật bởi JS -->
        </div>
    </div>

    <!-- ========================================
         JAVASCRIPT FILES
         ======================================== -->
    <!-- Main JavaScript - JS chính của ứng dụng -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <!-- WebRTC Camera JavaScript - JS hỗ trợ camera WebRTC -->
    <script src="{{ url_for('static', filename='js/webrtc-camera.js') }}"></script>

    <!-- ========================================
         SINGLE PAGE LAYOUT JAVASCRIPT
         ======================================== -->
    <script>
        // GLOBAL VARIABLES

        // Single Page Layout Management - Quản lý layout trang đơn
        let currentStep = 1; // Bước hiện tại trong workflow

        // STATUS MANAGEMENT FUNCTIONS

        // Update section status indicators - Cập nhật chỉ báo trạng thái section
        function updateSectionStatus(section, status, text) {
            // Lấy element chỉ báo trạng thái
            const statusIndicator = document.getElementById(`${section}Status`);
            // Lấy element text trạng thái
            const statusText = document.getElementById(`${section}StatusText`);

            // Cập nhật class cho chỉ báo (thay đổi màu sắc)
            if (statusIndicator) {
                statusIndicator.className = `status-indicator status-${status}`;
            }
            // Cập nhật text trạng thái
            if (statusText) {
                statusText.textContent = text;
            }
        }

        // Update workflow progress - Cập nhật tiến trình workflow
        function updateWorkflowProgress(step, status = 'processing') {
            // Cập nhật bước hiện tại
            currentStep = step;

            // Reset tất cả trạng thái về pending
            updateSectionStatus('camera', 'pending', 'Sẵn sàng');
            updateSectionStatus('ai', 'pending', 'Chờ ảnh');
            updateSectionStatus('results', 'pending', 'Chờ xử lý');

            // Cập nhật trạng thái dựa trên bước hiện tại
            switch(step) {
                case 1: // Bước chụp ảnh
                    updateSectionStatus('camera', status, status === 'processing' ? 'Đang chụp...' : 'Sẵn sàng');
                    break;
                case 2: // Bước tạo AI
                    updateSectionStatus('camera', 'complete', 'Đã chụp');
                    updateSectionStatus('ai', status, status === 'processing' ? 'Đang tạo AI...' : 'Sẵn sàng tạo');
                    break;
                case 3: // Bước hiển thị kết quả
                    updateSectionStatus('camera', 'complete', 'Đã chụp');
                    updateSectionStatus('ai', 'complete', 'Đã tạo AI');
                    updateSectionStatus('results', status, status === 'complete' ? 'Hoàn thành' : 'Đang xử lý');
                    break;
            }
        }

        // ========================================
        // DATA LOADING FUNCTIONS
        // ========================================

        // Load and display result data - Tải và hiển thị dữ liệu kết quả
        function loadAndDisplayResults() {
            console.log('🔄 Loading result data...');

            // Gọi API để lấy trạng thái session
            fetch('/api/session_status')
                .then(response => {
                    console.log('📡 Session status response:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📊 Session data received:', data);

                    // Kiểm tra nếu có dữ liệu session
                    if (data.status === 'success' && data.session) {
                        // Lấy thông tin card và ảnh đã tạo
                        const cardInfo = data.session.card_info || {};
                        const generatedImages = data.session.generated_images || [];

                        console.log('📄 Card info:', cardInfo);
                        console.log('🖼️ Generated images count:', generatedImages.length);

                        // Hiển thị dữ liệu lên UI
                        populateCardInfo(cardInfo);
                        populateGeneratedImages(generatedImages);

                        // Cập nhật tiến trình workflow dựa trên dữ liệu có sẵn
                        if (generatedImages.length > 0) {
                            updateWorkflowProgress(3, 'complete'); // Đã có ảnh AI
                        } else if (Object.keys(cardInfo).length > 0) {
                            updateWorkflowProgress(2, 'complete'); // Đã có thông tin card
                        }
                    } else {
                        console.log('⚠️ No session data available:', data.message || 'Unknown reason');
                    }
                })
                .catch(error => {
                    console.error('❌ Error loading result data:', error);
                });
        }

        // ========================================
        // UI POPULATION FUNCTIONS
        // ========================================

        // Populate card information - Điền thông tin card vào UI (không tự trigger animation)
        function populateCardInfo(cardInfo) {
            console.log('📄 Populating card info:', cardInfo);

            // Chỉ điền dữ liệu, không tự động hiển thị section
            // Animation sẽ được trigger bởi workflow chính

            // Danh sách các trường thông tin cần hiển thị
            const fields = ['Name', 'Title', 'Company', 'Email', 'Phone', 'Address'];

            // Lặp qua từng trường và cập nhật UI
            fields.forEach(field => {
                const element = document.getElementById(`card${field}`); // Lấy element theo ID
                if (element) {
                    // Lấy giá trị từ cardInfo, ưu tiên lowercase trước
                    const value = cardInfo[field.toLowerCase()] || cardInfo[field] || 'Chưa có dữ liệu';
                    element.textContent = value; // Cập nhật text content
                    console.log(`✅ Updated ${field}: ${value}`);
                } else {
                    console.error(`❌ Element not found: card${field}`);
                }
            });

            console.log('✅ Card info populated successfully');
        }

        // Populate generated images - Điền ảnh AI đã tạo vào UI
        function populateGeneratedImages(generatedImages) {
            console.log('🖼️ Populating generated images:', generatedImages);
            console.log('🖼️ Images array length:', generatedImages ? generatedImages.length : 'undefined');

            // Lấy container chứa ảnh
            const container = document.getElementById('generatedImagesDisplay');
            if (!container) {
                console.error('❌ Images container not found!');
                return false;
            }
            console.log('✅ Images container found:', container);

            // Debug: Kiểm tra parent section
            const parentSection = document.getElementById('imagesSection');
            if (parentSection) {
                console.log('🔍 Parent section state:');
                console.log('   - classList:', parentSection.classList.toString());
                console.log('   - display:', window.getComputedStyle(parentSection).display);
            }

            // Kiểm tra nếu có ảnh để hiển thị
            if (generatedImages && generatedImages.length > 0) {
                console.log(`🖼️ Displaying ${generatedImages.length} images`);

                // Chỉ điền dữ liệu, không tự động hiển thị section
                // Animation sẽ được trigger bởi workflow chính sau khi OCR hoàn thành

                container.innerHTML = ''; // Xóa nội dung cũ

                // Lặp qua từng ảnh và tạo HTML
                generatedImages.forEach((imageInfo, index) => {
                    // Tạo div chứa ảnh
                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-item';

                    // Xử lý các định dạng đường dẫn ảnh khác nhau
                    let imagePath;
                    if (typeof imageInfo === 'string') {
                        imagePath = imageInfo; // Nếu là string trực tiếp
                    } else if (imageInfo.image_path) {
                        imagePath = imageInfo.image_path; // Nếu có thuộc tính image_path
                    } else if (imageInfo.path) {
                        imagePath = imageInfo.path; // Nếu có thuộc tính path
                    } else {
                        console.warn('⚠️ Unknown image format:', imageInfo);
                        imagePath = imageInfo; // Fallback
                    }

                    // Tạo tên file để download
                    const filename = `ai_image_${index + 1}.jpg`;
                    console.log(`🖼️ Adding image ${index + 1}: ${imagePath}`);

                    // Tạo HTML cho ảnh
                    imageItem.innerHTML = `
                        <div class="image-display">
                            <img src="/${imagePath}" alt="Generated AI Image ${index + 1}" loading="lazy"
                                 onerror="console.error('Failed to load image: ${imagePath}')">
                            <div class="image-overlay">
                                AI Generated Image ${index + 1}
                            </div>
                        </div>
                        <div class="image-actions">
                            <a href="/${imagePath}" download="${filename}" class="download-btn">
                                📥 Tải về
                            </a>
                        </div>
                    `;

                    // Thêm vào container
                    container.appendChild(imageItem);
                });

                console.log('✅ Images populated successfully');
                return true;
            } else {
                // Hiển thị trạng thái rỗng khi không có ảnh
                console.log('⚠️ No images to display');
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">🖼️</div>
                        <p>Chưa có ảnh AI nào được tạo</p>
                        <p style="font-size: 14px; margin-top: var(--spacing-sm);">Chụp ảnh và tạo AI để xem kết quả</p>
                    </div>
                `;
                return false;
            }
        }

        // Force refresh results - Buộc làm mới kết quả
        function forceRefreshResults() {
            console.log('🔄 Force refreshing results...');

            // Gọi API nhiều lần để đảm bảo lấy được dữ liệu
            let attempts = 0;
            const maxAttempts = 5;

            const refreshInterval = setInterval(() => {
                attempts++;
                console.log(`🔄 Refresh attempt ${attempts}/${maxAttempts}`);

                loadAndDisplayResults();

                if (attempts >= maxAttempts) {
                    clearInterval(refreshInterval);
                    console.log('✅ Force refresh completed');
                }
            }, 2000); // Mỗi 2 giây
        }

        // ========================================
        // AUTO-SCROLL & PROGRESSIVE DISCLOSURE FUNCTIONS
        // ========================================

        // Auto scroll to element - Tự động cuộn đến element
        function autoScrollToElement(elementId, offset = 100) {
            const element = document.getElementById(elementId);
            if (element) {
                const elementPosition = element.offsetTop - offset;
                window.scrollTo({
                    top: elementPosition,
                    behavior: 'smooth'
                });
                console.log(`📍 Auto-scrolled to: ${elementId}`);
            } else {
                console.error(`❌ Element not found for scroll: ${elementId}`);
            }
        }

        // Show section with animation - Hiển thị section với animation
        function showSectionWithAnimation(elementId, animationClass = 'slide-in') {
            const element = document.getElementById(elementId);
            if (element) {
                // Remove hidden class and add visible class
                element.classList.remove('section-hidden');
                element.classList.add('section-visible', animationClass);

                console.log(`✨ Showed section: ${elementId} with ${animationClass}`);
                return true;
            } else {
                console.error(`❌ Element not found for animation: ${elementId}`);
                return false;
            }
        }

        // Hide section - Ẩn section
        function hideSectionWithAnimation(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.classList.remove('section-visible', 'slide-in', 'fade-in');
                element.classList.add('section-hidden');
                console.log(`🙈 Hidden section: ${elementId}`);
            }
        }

        // Progressive disclosure workflow - Workflow hiển thị từng bước (NO AUTO SCROLL)
        function startProgressiveDisclosure() {
            console.log('🎬 Starting progressive disclosure workflow (NO AUTO SCROLL)...');

            // Step 1: Hiển thị results section KHÔNG scroll
            setTimeout(() => {
                showSectionWithAnimation('resultsSection', 'fade-in');
                console.log('📍 Step 1: Results section shown (NO SCROLL)');
            }, 1000);

            // Step 2: Hiển thị OCR results (Card Info) TRƯỚC KHÔNG scroll + FETCH DATA
            setTimeout(() => {
                showSectionWithAnimation('cardInfoSection', 'slide-in');
                console.log('📄 Step 2: OCR Card Info section shown FIRST (NO SCROLL)');

                // Show loading state first
                showCardInfoLoading();

                // Fetch and display OCR results
                fetchOCRResults().then(ocrData => {
                    if (ocrData) {
                        console.log('✅ OCR data loaded and displayed');
                    }
                });
            }, 2000);
        }

        // Show images section - Hiển thị section ảnh (NO AUTO SCROLL) + FETCH DATA
        function showImagesSection() {
            console.log('🖼️ Step 3: Showing AI images section AFTER OCR (NO SCROLL)...');
            setTimeout(() => {
                showSectionWithAnimation('imagesSection', 'slide-in');
                console.log('🎨 AI Images section shown SECOND (NO SCROLL)');

                // Fetch and display generated images
                fetchGeneratedImages().then(imageData => {
                    if (imageData) {
                        console.log('✅ Generated images loaded and displayed');
                    }
                });
            }, 800); // Tăng delay để đảm bảo OCR đã hiển thị xong
        }

        // Show images section without fetching - Chỉ hiển thị section mà không fetch data
        function showImagesSectionOnly() {
            console.log('🖼️ Showing AI images section (data already populated)...');

            // Debug: Kiểm tra section state trước khi hiển thị
            const section = document.getElementById('imagesSection');
            if (section) {
                console.log('🔍 Section state before show:');
                console.log('   - classList:', section.classList.toString());
                console.log('   - display:', window.getComputedStyle(section).display);
                console.log('   - opacity:', window.getComputedStyle(section).opacity);
                console.log('   - maxHeight:', window.getComputedStyle(section).maxHeight);
            }

            const success = showSectionWithAnimation('imagesSection', 'slide-in');

            // Debug: Kiểm tra section state sau khi hiển thị
            if (section && success) {
                setTimeout(() => {
                    console.log('🔍 Section state after show:');
                    console.log('   - classList:', section.classList.toString());
                    console.log('   - display:', window.getComputedStyle(section).display);
                    console.log('   - opacity:', window.getComputedStyle(section).opacity);
                    console.log('   - maxHeight:', window.getComputedStyle(section).maxHeight);
                }, 100);
            }

            if (success) {
                console.log('✅ AI Images section displayed successfully');
            } else {
                console.error('❌ Failed to display AI Images section');
            }
            return success;
        }

        // Reset all sections - Reset tất cả sections về trạng thái ban đầu (NO AUTO SCROLL)
        function resetAllSections() {
            console.log('🔄 Resetting all sections (NO SCROLL)...');
            hideSectionWithAnimation('resultsSection');
            hideSectionWithAnimation('cardInfoSection');
            hideSectionWithAnimation('imagesSection');

            // NO auto scroll - giữ nguyên vị trí hiện tại
            console.log('📍 Staying at current position (NO SCROLL)');
        }

        // ========================================
        // BACKEND API INTEGRATION FUNCTIONS
        // ========================================

        // Show loading state for card info
        function showCardInfoLoading() {
            console.log('⏳ Showing loading state for card info...');
            const fields = ['cardName', 'cardTitle', 'cardCompany', 'cardEmail', 'cardPhone', 'cardAddress'];
            fields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.textContent = 'Đang tải...';
                    element.style.color = 'var(--color-text-secondary)';
                    element.style.fontStyle = 'italic';
                }
            });
        }

        // Update card information display with OCR results
        function updateCardInfoDisplay(ocrData) {
            console.log('📄 Updating card info display with OCR data:', ocrData);

            // Update individual fields with fallback values and loading states
            const updateField = (fieldId, value, fallback = 'Không có dữ liệu') => {
                const element = document.getElementById(fieldId);
                if (element) {
                    if (value === 'loading') {
                        element.textContent = 'Đang tải...';
                        element.style.color = 'var(--color-text-secondary)';
                        element.style.fontStyle = 'italic';
                    } else {
                        element.textContent = value || fallback;
                        element.style.color = value ? 'var(--color-text-primary)' : 'var(--color-text-secondary)';
                        element.style.fontStyle = 'normal';
                    }
                }
            };

            // Update all card fields
            updateField('cardName', ocrData.name || ocrData.full_name);
            updateField('cardTitle', ocrData.title || ocrData.position);
            updateField('cardCompany', ocrData.company || ocrData.organization);
            updateField('cardEmail', ocrData.email);
            updateField('cardPhone', ocrData.phone || ocrData.mobile);
            updateField('cardAddress', ocrData.address || ocrData.location);

            console.log('✅ Card info display updated successfully');
        }

        // Update generated images display
        function updateGeneratedImagesDisplay(imageData) {
            console.log('🖼️ Updating generated images display:', imageData);

            const imagesContainer = document.getElementById('generatedImagesDisplay');
            if (!imagesContainer) {
                console.error('❌ Images container not found');
                return;
            }

            // Clear existing content
            imagesContainer.innerHTML = '';

            if (imageData && imageData.images && imageData.images.length > 0) {
                imageData.images.forEach((imageUrl, index) => {
                    const imageElement = document.createElement('div');
                    imageElement.className = 'generated-image-item';
                    imageElement.innerHTML = `
                        <img src="${imageUrl}" alt="Generated Image ${index + 1}" class="generated-image">
                        <div class="image-actions">
                            <button class="apple-button apple-button-secondary" onclick="downloadImage('${imageUrl}', 'generated_image_${index + 1}')">
                                <span>💾</span> Tải xuống
                            </button>
                        </div>
                    `;
                    imagesContainer.appendChild(imageElement);
                });
                console.log(`✅ ${imageData.images.length} images displayed successfully`);
            } else {
                imagesContainer.innerHTML = `
                    <div class="empty-state">
                        <p>Chưa có ảnh được tạo</p>
                    </div>
                `;
                console.log('ℹ️ No images to display');
            }
        }

        // Download image function
        function downloadImage(imageUrl, filename) {
            console.log('💾 Downloading image:', imageUrl);
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = filename + '.png';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            console.log('✅ Image download initiated');
        }

        // Fetch OCR results from backend with retry logic
        function fetchOCRResults(retryCount = 0, maxRetries = 3) {
            console.log(`📡 Fetching OCR results from backend... (attempt ${retryCount + 1}/${maxRetries + 1})`);

            return fetch('/api/ocr_results')
                .then(response => {
                    console.log(`📡 OCR API response status: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log(`📄 OCR results received (attempt ${retryCount + 1}):`, data);

                    if (data.status === 'success' && data.ocr_data) {
                        console.log('✅ OCR data received successfully:', data.ocr_data);

                        // Kiểm tra chất lượng OCR data
                        const quality = data.quality || {};
                        console.log(`📊 OCR Quality: ${quality.fields_extracted || 0}/${quality.total_fields || 0} fields (${quality.percentage || 0}%)`);

                        updateCardInfoDisplay(data.ocr_data);

                        // Auto-play MP3 if available
                        if (data.audio_file) {
                            console.log('🎵 Audio file URL received:', data.audio_file);
                            playTTSAudio(data.audio_file);
                        } else {
                            console.log('⚠️ No audio file in OCR response');
                        }

                        return data.ocr_data;

                    } else if (data.status === 'error' && retryCount < maxRetries) {
                        // If no data available and we have retries left, wait and retry
                        console.log(`⏳ OCR data not ready, retrying in 2 seconds... (${retryCount + 1}/${maxRetries})`);
                        console.log(`🔍 Debug info:`, data.debug || 'No debug info');

                        return new Promise(resolve => {
                            setTimeout(() => {
                                resolve(fetchOCRResults(retryCount + 1, maxRetries));
                            }, 2000);
                        });
                    } else {
                        // Final attempt failed
                        console.error('❌ OCR fetch failed after all retries:', data);
                        throw new Error(data.message || 'OCR data not available after retries');
                    }
                })
                .catch(error => {
                    console.error('❌ Error fetching OCR results:', error);
                    if (retryCount < maxRetries) {
                        console.log(`⏳ Retrying OCR fetch in 2 seconds... (${retryCount + 1}/${maxRetries})`);
                        return new Promise(resolve => {
                            setTimeout(() => {
                                resolve(fetchOCRResults(retryCount + 1, maxRetries));
                            }, 2000);
                        });
                    } else {
                        showMessage('⚠️ Không thể lấy dữ liệu OCR sau nhiều lần thử. Vui lòng thử lại.', 'error');
                        return null;
                    }
                });
        }

        // Fetch generated images from backend with retry logic
        function fetchGeneratedImages(retryCount = 0, maxRetries = 5) {
            console.log(`📡 Fetching generated images from backend... (attempt ${retryCount + 1}/${maxRetries + 1})`);

            return fetch('/api/generated_images')
                .then(response => {
                    console.log(`📡 Images API response status: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('🖼️ Generated images received:', data);
                    if (data.status === 'success' && data.images && data.images.length > 0) {
                        updateGeneratedImagesDisplay(data);
                        return data.images;
                    } else if (data.status === 'error' && data.message && retryCount < maxRetries) {
                        // If no images available and we have retries left, wait and retry
                        console.log(`⏳ Generated images not ready, retrying in 3 seconds... (${retryCount + 1}/${maxRetries})`);
                        return new Promise(resolve => {
                            setTimeout(() => {
                                resolve(fetchGeneratedImages(retryCount + 1, maxRetries));
                            }, 3000);
                        });
                    } else {
                        throw new Error(data.message || 'Generated images not available');
                    }
                })
                .catch(error => {
                    console.error('❌ Error fetching generated images:', error);
                    if (retryCount < maxRetries) {
                        console.log(`⏳ Retrying images fetch in 3 seconds... (${retryCount + 1}/${maxRetries})`);
                        return new Promise(resolve => {
                            setTimeout(() => {
                                resolve(fetchGeneratedImages(retryCount + 1, maxRetries));
                            }, 3000);
                        });
                    } else {
                        showMessage('⚠️ Không thể lấy ảnh đã tạo sau nhiều lần thử. Vui lòng thử lại.', 'error');
                        return null;
                    }
                });
        }

        // ========================================
        // WORKFLOW STATUS TRACKING
        // ========================================

        // Track complete workflow status
        function trackWorkflowStatus() {
            console.log('📊 === WORKFLOW STATUS TRACKING ===');

            // Check session status
            fetch('/api/session_status')
                .then(response => response.json())
                .then(data => {
                    console.log('📊 Session Status:', data);

                    if (data.status === 'success' && data.session) {
                        const session = data.session;
                        console.log(`📊 Session ID: ${session.session_id}`);
                        console.log(`📊 Card Info Available: ${!!session.card_info && Object.keys(session.card_info).length > 0}`);
                        console.log(`📊 Generated Images Count: ${session.generated_images ? session.generated_images.length : 0}`);

                        // If we have card info, try to fetch and display it
                        if (session.card_info && Object.keys(session.card_info).length > 0) {
                            console.log('📊 Card info found in session, updating display...');
                            updateCardInfoDisplay(session.card_info);
                        }

                        // If we have generated images, try to fetch and display them
                        if (session.generated_images && session.generated_images.length > 0) {
                            console.log('📊 Generated images found in session, updating display...');
                            updateGeneratedImagesDisplay({
                                status: 'success',
                                images: session.generated_images,
                                count: session.generated_images.length
                            });
                        }
                    } else {
                        console.log('📊 No active session or session data');
                    }
                })
                .catch(error => {
                    console.error('📊 Error tracking workflow status:', error);
                });
        }

        // Monitor processing status
        function monitorProcessingStatus() {
            console.log('📊 Starting processing status monitor...');

            const checkStatus = () => {
                fetch('/api/session_status')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success' && data.session) {
                            const session = data.session;

                            // Check if processing is complete
                            if (session.card_info && Object.keys(session.card_info).length > 0) {
                                console.log('📊 OCR processing detected as complete');
                                fetchOCRResults();
                            }

                            if (session.generated_images && session.generated_images.length > 0) {
                                console.log('📊 AI generation detected as complete');
                                fetchGeneratedImages();
                            }
                        }
                    })
                    .catch(error => {
                        console.error('📊 Error monitoring status:', error);
                    });
            };

            // Check every 3 seconds for 60 seconds
            const interval = setInterval(checkStatus, 3000);
            setTimeout(() => {
                clearInterval(interval);
                console.log('📊 Processing status monitor stopped');
            }, 60000);
        }

        // ========================================
        // UTILITY FUNCTIONS
        // ========================================

        // Show toast notification
        function showMessage(message, type = 'info') {
            console.log(`📢 showMessage called: ${message} (${type})`);

            // Create toast container if not exists
            let toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    pointer-events: none;
                `;
                document.body.appendChild(toastContainer);
            }

            // Create toast element
            const toast = document.createElement('div');
            const toastId = 'toast_' + Date.now();
            toast.id = toastId;

            const bgColor = type === 'success' ? '#4CAF50' :
                           type === 'error' ? '#f44336' :
                           type === 'warning' ? '#ff9800' :
                           '#2196F3';

            toast.style.cssText = `
                background: ${bgColor};
                color: white;
                padding: 12px 20px;
                margin-bottom: 10px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                word-wrap: break-word;
                pointer-events: auto;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;

            // Add to container
            toastContainer.appendChild(toast);
            console.log(`📢 Toast created: ${message}`);

            // Animate in
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            // Auto remove after 4 seconds
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                        console.log(`📢 Toast removed: ${message}`);
                    }
                }, 300);
            }, 4000);

            console.log(`📢 ${type.toUpperCase()}: ${message}`);
        }

        // Download image - Tải ảnh về máy (function dự phòng)
        function downloadImage(imagePath, filename) {
            const link = document.createElement('a'); // Tạo element link ẩn
            link.href = '/' + imagePath;              // Đặt đường dẫn ảnh
            link.download = filename;                 // Đặt tên file download
            document.body.appendChild(link);          // Thêm vào DOM
            link.click();                            // Trigger click để download
            document.body.removeChild(link);         // Xóa khỏi DOM
        }

        // Play TTS Audio with notification on browser
        function playTTSAudio(audioPath) {
            console.log('🔊 playTTSAudio called with path:', audioPath);

            if (!audioPath) {
                console.log('⚠️ No audio path provided');
                showMessage('⚠️ Không có file âm thanh', 'warning');
                return;
            }

            try {
                console.log('🔊 Creating audio element...');
                // Create audio element for browser playback
                const audio = new Audio(audioPath);
                audio.volume = 0.8; // Set volume
                console.log('🔊 Audio element created, showing notification...');

                // Show notification
                showMessage('🔊 Đang phát âm thanh trên loa...', 'info');
                console.log('🔊 Notification shown, attempting to play...');

                // Play audio on browser speakers
                audio.play().then(() => {
                    console.log('✅ Audio playing started on browser speakers');
                    showMessage('🎵 Âm thanh đang phát...', 'success');
                }).catch(error => {
                    console.error('❌ Audio play failed:', error);
                    showMessage('⚠️ Không thể phát âm thanh trên loa', 'warning');
                });

                // Handle audio events
                audio.addEventListener('ended', () => {
                    console.log('✅ Audio playback completed');
                    showMessage('✅ Phát âm thanh hoàn tất', 'success');
                });

                audio.addEventListener('error', (e) => {
                    console.error('❌ Audio error:', e);
                    showMessage('⚠️ Lỗi phát âm thanh', 'warning');
                });

            } catch (error) {
                console.error('❌ Audio creation failed:', error);
                showMessage('⚠️ Lỗi tạo audio player', 'warning');
            }
        }

        // ========================================
        // SESSION MANAGEMENT FUNCTIONS
        // ========================================

        // Retry AI Generation - Thử lại tạo ảnh AI với OCR data có sẵn
        function retryAIGeneration() {
            console.log('🔄 Starting AI generation retry...');

            // Kiểm tra xem đã chọn style chưa
            const promptSelect = document.getElementById('promptSelect');
            if (!promptSelect || !promptSelect.value) {
                showMessage('⚠️ Vui lòng chọn style ảnh trước khi thử lại!', 'warning');
                return;
            }

            const selectedStyle = promptSelect.value;
            console.log(`🎨 Retry with style: ${selectedStyle}`);

            // Disable retry button
            const retryBtn = document.getElementById('retryAIBtn');
            if (retryBtn) {
                retryBtn.disabled = true;
                retryBtn.innerHTML = '<span>⏳</span> Đang tạo ảnh AI...';
            }

            // Show progress
            showMessage('🎨 Đang thử lại tạo ảnh AI...', 'info');

            // Call retry API
            fetch('/processing/retry_ai_generation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt_template: selectedStyle
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('🔄 Retry API response:', data);

                if (data.status === 'success') {
                    console.log('✅ AI generation retry successful!');
                    showMessage('✅ Ảnh AI đã được tạo thành công!', 'success');

                    // Display results
                    if (data.generated_images && data.generated_images.length > 0) {
                        populateGeneratedImages(data.generated_images);
                        showImagesSectionOnly(); // Sử dụng function mới không fetch data
                    }

                    // Hide retry button after success
                    if (retryBtn) {
                        retryBtn.style.display = 'none';
                    }

                } else {
                    console.error('❌ AI generation retry failed:', data.message);
                    showMessage(`❌ Thử lại thất bại: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('❌ Retry API Error:', error);
                showMessage('❌ Lỗi kết nối API. Vui lòng thử lại.', 'error');
            })
            .finally(() => {
                // Reset retry button
                if (retryBtn) {
                    retryBtn.disabled = false;
                    retryBtn.innerHTML = '<span>🎨</span> Tạo ảnh AI tiếp theo';
                }
            });
        }

        // Create New Images - Bắt đầu dự án mới (Reset session và bắt đầu lại)
        function createNewImages() {
            console.log('🎨🎨🎨 NEW FUNCTION: Starting new project (resetting session)...');
            alert('DEBUG: createNewImages function called!'); // Debug alert

            // Xác nhận với người dùng
            if (confirm('Bạn có chắc muốn bắt đầu dự án mới? Session hiện tại sẽ bị xóa.')) {
                console.log('✅ User confirmed to start new project');
                // Disable create button
                const createBtn = document.getElementById('createNewImagesBtn');
                if (createBtn) {
                    createBtn.disabled = true;
                    createBtn.innerHTML = '<span>⏳</span> Đang reset session...';
                }

                // Show progress
                showMessage('🔄 Đang bắt đầu dự án mới...', 'info');

                // Gọi API reset session
                fetch('/reset_session', {
                    method: 'POST'
                })
                .then(res => res.json())
                .then(data => {
                    if (data.status === 'success') {
                        console.log('✅ Session reset successfully for new project!');

                        // Reset UI về trạng thái ban đầu
                        resetAllSections(); // Reset tất cả sections
                        updateWorkflowProgress(1, 'pending');

                        // Xóa kết quả hiển thị
                        populateCardInfo({});           // Xóa thông tin card
                        populateGeneratedImages([]);    // Xóa ảnh đã tạo

                        // Reset các nút về trạng thái ban đầu
                        const combinedBtn = document.getElementById('combinedActionBtn');
                        if (combinedBtn) {
                            combinedBtn.disabled = false; // Kích hoạt lại nút
                            combinedBtn.innerHTML = '<span>⚡</span> Chụp ảnh & Tạo AI'; // Reset text
                        }

                        const retakeBtn = document.getElementById('retakeBtn');
                        if (retakeBtn) {
                            retakeBtn.style.display = 'none'; // Ẩn nút chụp lại
                        }

                        // Ẩn new project button
                        if (createBtn) {
                            createBtn.style.display = 'none';
                        }

                        // Ẩn progress bar
                        const progressBar = document.getElementById('progressBar');
                        if (progressBar) {
                            progressBar.style.display = 'none';
                        }

                        // Hiển thị thông báo thành công
                        showMessage('✅ Dự án mới đã được bắt đầu! Hãy chụp ảnh thẻ căn cước.', 'success');

                    } else {
                        console.error('❌ Session reset failed:', data.message);
                        showMessage(`❌ Không thể bắt đầu dự án mới: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('❌ Reset session error:', error);
                    showMessage('❌ Lỗi kết nối API. Vui lòng thử lại.', 'error');
                })
                .finally(() => {
                    // Reset create button
                    if (createBtn) {
                        createBtn.disabled = false;
                        createBtn.innerHTML = '<span>🆕</span> Dự án mới';
                    }
                });
            }
        }

        // Reset session - Reset session và UI về trạng thái ban đầu
        function resetSession() {
            // Xác nhận với người dùng
            if (confirm('Bạn có chắc muốn xóa session hiện tại?')) {
                // Gọi API reset session
                fetch('/reset_session', {
                    method: 'POST'
                })
                .then(res => res.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Reset UI về trạng thái ban đầu
                        resetAllSections(); // Reset tất cả sections
                        updateWorkflowProgress(1, 'pending');

                        // Xóa kết quả hiển thị
                        populateCardInfo({});           // Xóa thông tin card
                        populateGeneratedImages([]);    // Xóa ảnh đã tạo

                        // Reset các nút về trạng thái ban đầu
                        const combinedBtn = document.getElementById('combinedActionBtn');
                        if (combinedBtn) {
                            combinedBtn.disabled = false; // Kích hoạt lại nút
                            combinedBtn.innerHTML = '<span>⚡</span> Chụp ảnh & Tạo AI'; // Reset text
                        }

                        const retakeBtn = document.getElementById('retakeBtn');
                        if (retakeBtn) {
                            retakeBtn.style.display = 'none'; // Ẩn nút chụp lại
                        }

                        // Ẩn new project button
                        const createBtn = document.getElementById('createNewImagesBtn');
                        if (createBtn) {
                            createBtn.style.display = 'none';
                        }

                        // Ẩn progress bar
                        const progressBar = document.getElementById('progressBar');
                        if (progressBar) {
                            progressBar.style.display = 'none';
                        }

                        // Hiển thị thông báo thành công
                        showMessage('✅ Session đã được reset thành công!', 'success');
                    }
                })
                .catch(error => {
                    // Hiển thị thông báo lỗi
                    showMessage('❌ Lỗi reset session: ' + error, 'error');
                });
            }
        }

        // ========================================
        // COMBINED WORKFLOW FUNCTIONS
        // ========================================

        // Combined capture and generate - NEW UNIFIED WORKFLOW
        function combinedCaptureAndGenerate() {
            console.log('🚀 Starting NEW Combined Capture & Generate Workflow...');

            // Kiểm tra xem đã chọn style chưa
            const promptSelect = document.getElementById('promptSelect');
            if (!promptSelect || !promptSelect.value) {
                showMessage('⚠️ Vui lòng chọn style ảnh trước khi bắt đầu!', 'warning');
                return;
            }

            const selectedStyle = promptSelect.value;
            console.log(`🎨 Selected style: ${selectedStyle}`);

            // Reset all sections trước khi bắt đầu
            resetAllSections();

            // Vô hiệu hóa nút để tránh click nhiều lần
            const combinedBtn = document.getElementById('combinedActionBtn');
            if (combinedBtn) {
                combinedBtn.disabled = true;
                combinedBtn.innerHTML = '<span>⏳</span> Đang xử lý...';
            }

            // Hiển thị progress bar
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            if (progressBar) {
                progressBar.style.display = 'block';
                progressFill.style.width = '10%';
            }

            // Bắt đầu progressive disclosure workflow
            startProgressiveDisclosure();

            // Cho phép scroll khi bắt đầu workflow
            document.body.classList.remove('no-scroll');
            document.body.classList.add('allow-scroll');

            // NEW UNIFIED API CALL - Handles everything in one request
            console.log('🚀 Calling unified capture & generate API...');
            if (progressFill) progressFill.style.width = '20%';

            // Call the new combined API endpoint
            fetch('/api/combined_capture_generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt_template: selectedStyle
                })
            })
            .then(response => {
                console.log('📡 API Response received:', response.status);
                if (progressFill) progressFill.style.width = '50%';
                return response.json();
            })
            .then(data => {
                console.log('📊 API Response data:', data);
                console.log('🔍 Response keys:', Object.keys(data));
                console.log('🔍 Status:', data.status);
                console.log('🔍 Has card_info:', !!data.card_info);
                console.log('🔍 Has generated_images:', !!data.generated_images);
                console.log('🔍 Generated images count:', data.generated_images ? data.generated_images.length : 0);

                if (progressFill) progressFill.style.width = '80%';

                if (data.status === 'success') {
                    console.log('✅ Combined workflow completed successfully!');

                    // Update progress
                    if (progressFill) progressFill.style.width = '100%';

                    // Display results with error handling
                    try {
                        displayWorkflowResults(data);
                        console.log('✅ Results displayed successfully');
                    } catch (displayError) {
                        console.error('❌ Error displaying results:', displayError);
                        showMessage('⚠️ Workflow hoàn thành nhưng có lỗi hiển thị. Vui lòng refresh trang.', 'warning');
                    }

                } else if (data.status === 'partial_success') {
                    console.log('⚠️ Partial success: OCR completed, AI generation failed');

                    // Update progress
                    if (progressFill) progressFill.style.width = '70%';

                    // Display OCR results
                    if (data.card_info) {
                        populateCardInfo(data.card_info);
                        showCardInfoSection();
                    }

                    // Show retry button for AI generation
                    const retryBtn = document.getElementById('retryAIBtn');
                    if (retryBtn) {
                        retryBtn.style.display = 'inline-block';
                    }

                    // Show partial success message
                    showMessage('⚠️ OCR hoàn thành. Tạo ảnh AI thất bại - có thể thử lại.', 'warning');

                } else {
                    console.error('❌ Combined workflow failed:', data.message);
                    showMessage(`❌ Lỗi: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                console.error('❌ API Error:', error);
                showMessage('❌ Lỗi kết nối API. Vui lòng thử lại.', 'error');
            })
            .finally(() => {
                // Reset UI regardless of success/failure
                setTimeout(() => {
                    if (progressBar) progressBar.style.display = 'none';
                    if (progressFill) progressFill.style.width = '0%';

                    if (combinedBtn) {
                        combinedBtn.disabled = false;
                        combinedBtn.innerHTML = '<span>⚡</span> Chụp ảnh & Tạo AI';
                    }

                    // Show retake button
                    const retakeBtn = document.getElementById('retakeBtn');
                    if (retakeBtn) {
                        retakeBtn.style.display = 'inline-block';
                    }
                }, 1000);
            });
        }

        // NEW FUNCTION: Display workflow results from combined API
        function displayWorkflowResults(data) {
            console.log('📊 Displaying workflow results:', data);

            try {
                // Display card info if available
                if (data.card_info) {
                    console.log('📄 Displaying card info...');
                    try {
                        populateCardInfo(data.card_info);
                        showCardInfoSection();
                        console.log('✅ Card info displayed successfully');
                    } catch (cardError) {
                        console.error('❌ Error displaying card info:', cardError);
                    }
                }

                // Display generated images if available
                if (data.generated_images && data.generated_images.length > 0) {
                    console.log('🖼️ Displaying generated images...');
                    console.log('🖼️ Images data:', data.generated_images);

                    try {
                        const populateSuccess = populateGeneratedImages(data.generated_images);
                        if (populateSuccess) {
                            const showSuccess = showImagesSectionOnly(); // Sử dụng function mới không fetch data
                            if (showSuccess) {
                                console.log('✅ Generated images displayed successfully');
                            } else {
                                console.error('❌ Failed to show images section');
                                showMessage('⚠️ Ảnh AI đã được tạo nhưng không thể hiển thị section. Vui lòng refresh trang.', 'warning');
                            }
                        } else {
                            console.error('❌ Failed to populate images');
                            showMessage('⚠️ Ảnh AI đã được tạo nhưng không thể populate. Vui lòng refresh trang.', 'warning');
                        }

                        // Show "New Project" button when images are successfully generated
                        const createBtn = document.getElementById('createNewImagesBtn');
                        if (createBtn) {
                            createBtn.style.display = 'inline-block';
                            console.log('✅ New project button shown');
                        }
                    } catch (imagesError) {
                        console.error('❌ Error displaying generated images:', imagesError);
                        showMessage('⚠️ Ảnh AI đã được tạo nhưng có lỗi hiển thị. Vui lòng refresh trang.', 'warning');
                    }
                } else {
                    console.log('⚠️ No generated images in response');
                    console.log('🔍 Response data keys:', Object.keys(data));
                }

                // Show success message
                showMessage('✅ Hoàn thành! Ảnh AI đã được tạo thành công.', 'success');

                console.log('✅ Workflow results displayed successfully');

            } catch (error) {
                console.error('❌ Error in displayWorkflowResults:', error);
                showMessage('⚠️ Có lỗi khi hiển thị kết quả. Vui lòng kiểm tra console.', 'warning');
                throw error; // Re-throw để trigger catch block ở level cao hơn
            }
        }

        // ========================================
        // ENHANCED WORKFLOW FUNCTIONS
        // ========================================

        // Enhanced capture function with workflow updates - Function chụp ảnh tích hợp workflow
        function enhancedSingleCapture() {
            // Cập nhật trạng thái đang xử lý bước 1
            updateWorkflowProgress(1, 'processing');

            // Gọi function chụp ảnh gốc
            if (typeof singleCapture === 'function') {
                singleCapture(); // Thực hiện chụp ảnh

                // Cập nhật tiến trình sau khi chụp (delay 2 giây)
                setTimeout(() => {
                    updateWorkflowProgress(2, 'pending');  // Chuyển sang bước 2
                    loadAndDisplayResults();               // Tải kết quả OCR nếu có
                }, 2000);
            }
        }

        // Enhanced AI generation with workflow updates - Function tạo AI tích hợp workflow
        function enhancedGenerateAI() {
            // Cập nhật trạng thái đang xử lý bước 2
            updateWorkflowProgress(2, 'processing');

            // Gọi function tạo AI gốc
            if (typeof generateAI === 'function') {
                const originalOnClick = generateAI;
                originalOnClick(); // Thực hiện tạo AI

                // Theo dõi quá trình hoàn thành
                setTimeout(() => {
                    updateWorkflowProgress(3, 'processing'); // Chuyển sang bước 3 đang xử lý
                    setTimeout(() => {
                        loadAndDisplayResults();             // Tải kết quả
                        updateWorkflowProgress(3, 'complete'); // Hoàn thành bước 3
                    }, 3000); // Delay 3 giây để AI hoàn thành
                }, 1000); // Delay 1 giây trước khi chuyển bước
            }
        }

        // ========================================
        // INITIALIZATION FUNCTIONS
        // ========================================

        // Initialize single page layout - Khởi tạo layout trang đơn
        function initializeSinglePage() {
            console.log('🚀 Initializing single page layout...');

            // Đảm bảo results section ẩn ban đầu
            resetAllSections();

            // Tải dữ liệu ban đầu (nhưng không hiển thị nếu chưa có workflow)
            loadAndDisplayResults();

            // Track workflow status on page load
            setTimeout(() => {
                trackWorkflowStatus();
            }, 1000);

            // Thiết lập function cho nút gộp mới
            const combinedBtn = document.getElementById('combinedActionBtn');
            if (combinedBtn) {
                combinedBtn.onclick = combinedCaptureAndGenerate;
                console.log('✅ Combined action button initialized');
            }

            // Thiết lập cập nhật định kỳ (mỗi 10 giây) - nhưng không trigger animations
            setInterval(() => {
                // Chỉ load data, không trigger animations tự động
                loadAndDisplayResults();
            }, 10000);

            console.log('✅ Single page layout initialized with progressive disclosure');
        }

        // ========================================
        // DEBUG FUNCTIONS (for testing)
        // ========================================

        // Test auto-scroll workflow - Test workflow auto-scroll (OCR trước, AI sau)
        function testAutoScrollWorkflow() {
            console.log('🧪 Testing sequential workflow (OCR first, AI second)...');
            resetAllSections();

            // Test OCR results trước
            setTimeout(() => {
                startProgressiveDisclosure(); // Hiển thị OCR results
                console.log('📄 OCR results should show first');
            }, 1000);

            // Test AI images sau
            setTimeout(() => {
                showImagesSection(); // Hiển thị AI images
                console.log('🎨 AI images should show second');
            }, 6000);
        }

        // Test individual sections - Test từng section riêng
        function testOCRSection() {
            console.log('🧪 Testing OCR section only...');
            resetAllSections();
            setTimeout(() => startProgressiveDisclosure(), 1000);
        }

        function testAISection() {
            console.log('🧪 Testing AI section only...');
            setTimeout(() => showImagesSection(), 1000);
        }

        // Make functions available globally for debugging
        window.testAutoScrollWorkflow = testAutoScrollWorkflow;
        window.testOCRSection = testOCRSection;
        window.testAISection = testAISection;
        window.resetAllSections = resetAllSections;
        window.showSectionWithAnimation = showSectionWithAnimation;
        window.autoScrollToElement = autoScrollToElement;
        window.startProgressiveDisclosure = startProgressiveDisclosure;
        window.showImagesSection = showImagesSection;

        // ========================================
        // PAGE LOAD EVENT
        // ========================================

        // Initialize on page load - Khởi tạo khi trang được tải
        document.addEventListener('DOMContentLoaded', function() {
            initializeSinglePage(); // Gọi function khởi tạo

            // Vô hiệu hóa scroll ban đầu
            document.body.classList.add('no-scroll');
            document.body.classList.remove('allow-scroll');
        });
    </script>

    <!-- ========================================
         END OF JAVASCRIPT
         ======================================== -->
</body>
</html>

<!-- ========================================
     END OF HTML DOCUMENT

     HƯỚNG DẪN CHỈNH SỬA GIAO DIỆN:

     1. THAY ĐỔI MÀU SẮC:
        - Tìm các biến CSS như var(--color-primary), var(--bg-card)
        - Thay đổi trong file apple-design.css hoặc thêm style inline

     2. THAY ĐỔI ICON:
        - Tìm các emoji như 📸, 🎨, 👤, 📄
        - Thay thế bằng emoji khác hoặc icon font

     3. THAY ĐỔI TEXT:
        - Tìm text trong các comment "có thể thay đổi"
        - Sửa trực tiếp trong HTML

     4. THAY ĐỔI LAYOUT:
        - Tìm các class CSS như .camera-grid, .controls-grid
        - Sửa grid-template-columns để thay đổi số cột

     5. THAY ĐỔI KÍCH THƯỚC:
        - Tìm các thuộc tính như font-size, padding, margin
        - Sử dụng các biến spacing như var(--spacing-lg)

     6. THÊM/XÓA SECTION:
        - Copy/paste cấu trúc .workflow-section
        - Cập nhật JavaScript tương ứng

     ======================================== -->
